/**
 * Unit tests for Background Rendering System in Royal Portrait Builder
 * Tests background layer integration, proper layering order, and artifact prevention
 */

// Mock the royal-portrait-complete.js module
const fs = require('fs');
const path = require('path');

// Read the actual implementation
const portraitBuilderCode = fs.readFileSync(path.join(__dirname, '../assets/royal-portrait-complete.js'), 'utf8');

// Execute the code in a controlled environment
eval(portraitBuilderCode);

describe('Background Rendering System', () => {
  let mockCanvas;
  let mockContext;
  let portraitCanvas;

  beforeEach(() => {
    // Mock analytics
    global.window = global.window || {};
    global.window.PortraitAnalyticsInstance = {
      trackPortraitBuilderStart: jest.fn(),
      trackBreedSelected: jest.fn(),
      trackCostumeSelected: jest.fn(),
      trackBackgroundSelected: jest.fn(),
      trackFrameSelected: jest.fn(),
      trackSizeSelected: jest.fn(),
      trackShopifyFunnelProgression: jest.fn(),
    };

    // Mock canvas context methods with property tracking
    let currentFillStyle = '#ffffff';
    mockContext = {
      get fillStyle() {
        return currentFillStyle;
      },
      set fillStyle(value) {
        currentFillStyle = value;
      },
      globalAlpha: 1,
      globalCompositeOperation: 'source-over',
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      drawImage: jest.fn(),
      beginPath: jest.fn(),
      closePath: jest.fn(),
      fill: jest.fn(),
      ellipse: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      getTransform: jest.fn(() => ({ a: 1, b: 0, c: 0, d: 1, e: 0, f: 0 })),
    };

    // Mock canvas element with DOM properties
    mockCanvas = {
      width: 300,
      height: 300,
      style: {
        display: 'block',
        width: '300px',
        height: '300px',
      },
      parentElement: {
        style: {
          display: 'block',
        },
      },
      getContext: jest.fn(() => mockContext),
      toDataURL: jest.fn(() => 'data:image/png;base64,mock'),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };

    // Create PortraitCanvas instance
    const PortraitCanvas = global.PortraitCanvas;
    portraitCanvas = new PortraitCanvas(mockCanvas);

    // Configure for tests
    portraitCanvas.config.canvasSize = 300; // Match test expectations

    // Re-initialize canvas with correct size
    portraitCanvas.initCanvas();

    // Enable rendering for tests (must be after initCanvas which calls hideCanvas)
    portraitCanvas.renderingEnabled = true;
    portraitCanvas.canvasVisible = true;
    portraitCanvas.isVisible = true;
  });

  afterEach(() => {
    delete global.window.PortraitAnalyticsInstance;
  });

  describe('Background Layer Integration', () => {
    test('should integrate background layer into canvas drawing pipeline', async () => {
      // Set a color background
      await portraitCanvas.setBackgroundLayer('color', '#ff0000');

      expect(portraitCanvas.layers.background).toEqual({
        type: 'color',
        value: '#ff0000',
      });
      expect(portraitCanvas.needsRedraw).toBe(true);
    });

    test('should handle color background rendering', async () => {
      await portraitCanvas.setBackgroundLayer('color', '#00ff00');

      // Verify background layer is set correctly
      expect(portraitCanvas.layers.background).toEqual({
        type: 'color',
        value: '#00ff00',
      });

      // Trigger render with step 3 to include background
      portraitCanvas.drawLayers(3);

      // Verify canvas was filled (should be called multiple times: clear + background)
      expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 300, 300);
      expect(mockContext.fillRect).toHaveBeenCalledTimes(4); // Multiple operations including clear + background
    });

    test('should handle image background rendering', async () => {
      // Mock image loading
      const mockImage = {
        complete: true,
        width: 400,
        height: 400,
        src: 'mock-background.jpg',
      };

      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockImage);
      jest.spyOn(portraitCanvas, 'drawImageCenteredOptimized').mockImplementation(() => {});

      await portraitCanvas.setBackgroundLayer('image', 'mock-background.jpg', 1.2);

      expect(portraitCanvas.layers.background).toEqual({
        type: 'image',
        img: mockImage,
        scale: 1.2,
      });

      // Trigger render
      portraitCanvas.drawLayers(3);

      // Verify image was drawn with correct parameters
      expect(portraitCanvas.drawImageCenteredOptimized).toHaveBeenCalledWith(
        mockContext,
        mockImage,
        150, // centerX
        150, // centerY
        1.2 // scale
      );
    });

    test('should handle background layer errors gracefully', async () => {
      jest.spyOn(portraitCanvas, 'loadImage').mockRejectedValue(new Error('Image load failed'));

      await expect(portraitCanvas.setBackgroundLayer('image', 'invalid-url.jpg')).rejects.toThrow('Image load failed');

      // Background layer should not be set on error
      expect(portraitCanvas.layers.background).toBeUndefined();
    });

    test('should clear background layer correctly', () => {
      // Set background first
      portraitCanvas.layers.background = { type: 'color', value: '#ff0000' };

      portraitCanvas.clearBackgroundLayer();

      expect(portraitCanvas.layers.background).toBeNull();
      expect(portraitCanvas.needsRedraw).toBe(true);
    });
  });

  describe('Proper Layer Ordering', () => {
    test('should render layers in correct order: background → breed → costume → frame', async () => {
      const renderOrder = [];

      // Track fillRect calls to identify background rendering
      const originalFillRect = mockContext.fillRect;
      mockContext.fillRect = jest.fn((...args) => {
        // Background rendering is identified by full canvas fillRect with background color
        if (
          args[0] === 0 &&
          args[1] === 0 &&
          args[2] === 300 &&
          args[3] === 300 &&
          mockContext.fillStyle === '#ffffff'
        ) {
          renderOrder.push('background');
        }
        return originalFillRect.apply(mockContext, args);
      });

      jest.spyOn(portraitCanvas, 'drawImageCenteredOptimized').mockImplementation((ctx, img, x, y, scale) => {
        if (img && img.src) {
          if (img.src.includes('breed')) renderOrder.push('breed');
          else if (img.src.includes('costume')) renderOrder.push('costume');
          else if (img.src.includes('frame')) renderOrder.push('frame');
        }
      });

      // Set up all layers
      await portraitCanvas.setBackgroundLayer('color', '#ffffff');

      const mockBreedImage = { complete: true, width: 200, height: 200, src: 'breed-image.jpg' };
      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockBreedImage);
      await portraitCanvas.setBreedLayer({ id: 'golden-retriever' }, 1.0);

      const mockCostumeImage = { complete: true, width: 150, height: 150, src: 'costume-image.png' };
      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockCostumeImage);
      await portraitCanvas.setCostumeLayer({ id: 'royal-crown', name: 'Royal Crown' });

      const mockFrameImage = { complete: true, width: 300, height: 300, src: 'frame-image.png' };
      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockFrameImage);
      await portraitCanvas.setFrameLayer({ id: 'classic-gold' }, 1.0);

      // Clear render order before final render
      renderOrder.length = 0;

      // Render all layers (step 4 includes all layers)
      portraitCanvas.drawLayers(4);

      // Verify correct rendering order
      expect(renderOrder).toEqual(['background', 'breed', 'costume', 'frame']);
    });

    test('should only render background in step 3+', () => {
      portraitCanvas.layers.background = { type: 'color', value: '#ff0000' };

      // Clear previous calls
      mockContext.fillRect.mockClear();

      // Step 2 should not render background color
      portraitCanvas.drawLayers(2);
      // Should only have canvas clear call, not background color
      expect(mockContext.fillStyle).not.toBe('#ff0000');

      // Clear and test step 3
      mockContext.fillRect.mockClear();

      // Step 3 should render background
      portraitCanvas.drawLayers(3);
      expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 300, 300);
      expect(mockContext.fillStyle).toBe('#ff0000');
    });

    test('should render background behind breed layer', async () => {
      const drawCalls = [];

      // Track all drawing operations
      mockContext.fillRect = jest.fn(() => drawCalls.push('background-fill'));
      jest.spyOn(portraitCanvas, 'drawImageCenteredOptimized').mockImplementation(() => {
        drawCalls.push('breed-image');
      });

      // Set background and breed
      await portraitCanvas.setBackgroundLayer('color', '#0000ff');

      const mockBreedImage = { complete: true, width: 200, height: 200, src: 'breed.jpg' };
      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockBreedImage);
      await portraitCanvas.setBreedLayer({ id: 'labrador' }, 1.0);

      portraitCanvas.drawLayers(3);

      // Background should be drawn before breed
      expect(drawCalls.indexOf('background-fill')).toBeLessThan(drawCalls.indexOf('breed-image'));
    });
  });

  describe('Background Switching Without Artifacts', () => {
    test('should switch between color backgrounds without artifacts', async () => {
      // Set initial background
      await portraitCanvas.setBackgroundLayer('color', '#ff0000');

      // Clear previous calls and render
      mockContext.fillRect.mockClear();
      portraitCanvas.drawLayers(3);

      // Check that background was rendered
      expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 300, 300);
      expect(portraitCanvas.layers.background.value).toBe('#ff0000');

      // Switch to different color
      await portraitCanvas.setBackgroundLayer('color', '#00ff00');

      // Clear previous calls and render
      mockContext.fillRect.mockClear();
      portraitCanvas.drawLayers(3);

      expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 300, 300);
      expect(portraitCanvas.layers.background.value).toBe('#00ff00');
    });

    test('should switch from color to image background without artifacts', async () => {
      // Start with color background
      await portraitCanvas.setBackgroundLayer('color', '#ff0000');
      portraitCanvas.drawLayers(3);

      // Switch to image background
      const mockImage = { complete: true, width: 400, height: 400, src: 'bg.jpg' };
      jest.spyOn(portraitCanvas, 'loadImage').mockResolvedValue(mockImage);
      jest.spyOn(portraitCanvas, 'drawImageCenteredOptimized').mockImplementation(() => {});

      await portraitCanvas.setBackgroundLayer('image', 'bg.jpg', 1.0);
      portraitCanvas.drawLayers(3);

      expect(portraitCanvas.layers.background.type).toBe('image');
      expect(portraitCanvas.drawImageCenteredOptimized).toHaveBeenCalled();
    });

    test('should handle rapid background changes without artifacts', async () => {
      const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'];

      for (const color of colors) {
        await portraitCanvas.setBackgroundLayer('color', color);
        // needsRedraw should be true after setting background
        expect(portraitCanvas.needsRedraw).toBe(true);

        portraitCanvas.drawLayers(3);

        expect(portraitCanvas.layers.background.value).toBe(color);
        // needsRedraw should be false after successful render
        expect(portraitCanvas.needsRedraw).toBe(false);
      }

      // Final render should use the last color
      expect(portraitCanvas.layers.background.value).toBe('#ff00ff');
    });

    test('should clear canvas properly before rendering new background', () => {
      portraitCanvas.layers.background = { type: 'color', value: '#ff0000' };
      portraitCanvas.drawLayers(3);

      // Verify canvas is cleared with default background before rendering
      expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 300, 300);
    });

    test('should maintain performance during background switching', async () => {
      const startTime = performance.now();

      // Perform multiple background switches
      for (let i = 0; i < 10; i++) {
        await portraitCanvas.setBackgroundLayer(
          'color',
          `#${i.toString(16)}${i.toString(16)}${i.toString(16)}${i.toString(16)}${i.toString(16)}${i.toString(16)}`
        );
        portraitCanvas.drawLayers(3);
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Should complete within reasonable time (less than 100ms for 10 switches)
      expect(totalTime).toBeLessThan(100);
    });
  });

  describe('Layer State Management', () => {
    test('should track needsRedraw flag correctly', async () => {
      expect(portraitCanvas.needsRedraw).toBeFalsy();

      await portraitCanvas.setBackgroundLayer('color', '#ff0000');
      expect(portraitCanvas.needsRedraw).toBe(true);

      portraitCanvas.drawLayers(3);
      expect(portraitCanvas.needsRedraw).toBe(false);
    });

    test('should maintain layer state consistency', async () => {
      await portraitCanvas.setBackgroundLayer('color', '#ff0000');

      expect(portraitCanvas.layers.background).toEqual({
        type: 'color',
        value: '#ff0000',
      });

      // State should persist after rendering
      portraitCanvas.drawLayers(3);
      expect(portraitCanvas.layers.background).toEqual({
        type: 'color',
        value: '#ff0000',
      });
    });

    test('should handle layer diagnostics correctly', () => {
      portraitCanvas.layers.background = { type: 'color', value: '#ff0000' };
      portraitCanvas.layers.breed = { img: { src: 'breed.jpg' }, scale: 1.0 };

      const diagnostics = portraitCanvas.diagnoseCanvasState();

      expect(diagnostics.layers.background).toBe(true);
      expect(diagnostics.layers.breed).toBe(true);
      expect(diagnostics.layers.costume).toBe(false);
      expect(diagnostics.layers.frame).toBe(false);
    });
  });

  describe('Performance Optimization', () => {
    test('should skip redraw when not needed', () => {
      // Clear any previous calls from setup
      mockContext.fillRect.mockClear();

      portraitCanvas.needsRedraw = false;
      portraitCanvas.performanceMetrics = { renderCount: 1 };

      portraitCanvas.drawLayers(3);

      // Should not have called any drawing operations
      expect(mockContext.fillRect).not.toHaveBeenCalled();
    });

    test('should track performance metrics for background rendering', async () => {
      portraitCanvas.layerTimings = { operations: [] };

      await portraitCanvas.setBackgroundLayer('color', '#ff0000');
      portraitCanvas.drawLayers(3);

      // Should have timing data for background layer
      const backgroundTiming = portraitCanvas.layerTimings.operations.find((op) => op.operation === 'background_layer');
      expect(backgroundTiming).toBeDefined();
      expect(backgroundTiming.duration).toBeGreaterThanOrEqual(0);
    });
  });
});
